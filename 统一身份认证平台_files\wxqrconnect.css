.impowerBox,
.impowerBox .status_icon,
.impowerBox .status_txt {
    display: inline-block;
    vertical-align: middle
}

a {
    outline: 0
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
    margin: 0;
    font-weight: 400
}

a img,
fieldset {
    border: 0
}

body {
    font-family: "Microsoft Yahei";
    color: #333;
    background: 0 0
}

.impowerBox {
    line-height: 1.5;
    position: relative;
    width: 100%;
    z-index: 1;
    text-align: center;
    background: #fff;
    padding: 20px 0 20px 0;
    border-radius: 10px;
}

.impowerBox .title {
    text-align: center;
    font-size: 20px
}

.impowerBox .qrcode {
    width: 100%;
    height:100%;
    /* margin-top: -8px; */
    /*border:1px solid #E2E2E2;*/
    background-color: #fff;
    padding: 5px;
}

.impowerBox .info {
    width: 280px;
    margin: 0 auto
}

.impowerBox .status {
    padding: 7px 0;
}

.impowerBox .status.normal {
    margin-top: 15px;
    background-color: #232323;
    border-radius: 100px;
    -moz-border-radius: 100px;
    -webkit-border-radius: 100px;
    box-shadow: inset 0 5px 10px -5px #191919, 0 1px 0 0 #444;
    -moz-box-shadow: inset 0 5px 10px -5px #191919, 0 1px 0 0 #444;
    -webkit-box-shadow: inset 0 5px 10px -5px #191919, 0 1px 0 0 #444
}

.impowerBox .status.status_browser {
    text-align: center
}

.impowerBox .status p {
    font-size: 13px
}

.impowerBox .status_icon {
    margin-right: 5px
}

.impowerBox .status_txt p {
    top: -2px;
    position: relative;
    margin: 0
}

.impowerBox .icon38_msg {
    display: inline-block;
    width: 38px;
    height: 38px
}

.impowerBox .icon38_msg.succ {
    background: url(../images/icon_btn.png)0 -46px no-repeat
}

.impowerBox .icon38_msg.warn {
    background: url(../images/icon_btn.png)0 -87px no-repeat
}

.wrp_code {
    position: relative;
    margin: 0px auto;
    width: 200px;
}

.qrdescbox {
    width: 100%;
    position: absolute;
    /* color: #000; */
    color: #333;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 2;
    font-size: 14px;
    display: none;
}

.qr_confirm {
    position: absolute;
    /*color: #000;*/
    color: #333;
    /*top:50%;
    left: 21%;*/
    top: 50%;
    left: 50%;
    z-index: 2;
    font-size: 14px;
    padding: 0px 15px;
    display: none;
}

.qr_cancel {
    position: absolute;
    /*color: #000;*/
    color: #333;
    /*top:50%;
    left: 21%;*/
    /* top: 40%; */
    left: 0%;
    z-index: 2;
    font-size: 14px;
    padding: 0px 15px;
    display: none;
}

.qr_mask {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, .95);
    position: absolute;
    display: none;
}

.refresh {
    color: #00C59B;
    cursor: pointer;
    text-decoration: none;
}

.fresh_icon {
    font-size: 30px !important;
    color: #F59A23;
}

.qr_error {
    position: absolute;
    /*color: #000;*/
    color: #333;
    /*top:50%;
    left: 21%;*/
    /* top: 28%; */
    left: 32%;
    z-index: 2;
    font-size: 14px;
    padding: 0px 15px;
    display: none;
}

.qr_disabled {
    position: absolute;
    /*color: #000;*/
    color: #333;
    /*top:50%;
    left: 21%;*/
    top: 28%;
    left: 32%;
    z-index: 2;
    font-size: 14px;
    padding: 0px 15px;
    display: none;
}

.cgi_valid {
    position: absolute;
    /*color: #000;*/
    color: #333;
    /*top:50%;
    left: 21%;*/
    top: 28%;
    left: 26%;
    z-index: 2;
    font-size: 14px;
    padding: 0px 15px;
}