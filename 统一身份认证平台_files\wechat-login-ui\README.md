# 微信扫码登录UI界面

这是一个基于原微信扫码登录页面的静态UI界面项目。

## 项目结构

```
wechat-login-ui/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件
├── js/
│   └── main.js         # JavaScript文件
├── images/
│   └── qr-placeholder.png  # 二维码占位图片
└── README.md           # 项目说明
```

## 功能特点

- 完全静态的HTML/CSS/JS实现
- 保持与原页面相同的UI布局和视觉效果
- 所有代码都有详细注释，便于修改
- 使用空白图片替代真实二维码
- 不需要联网功能

## 使用方法

直接在浏览器中打开 `index.html` 文件即可查看效果。

## 自定义修改

所有UI组件都有详细的注释说明，您可以根据需要修改：
- 修改颜色和样式：编辑 `css/style.css`
- 修改页面内容：编辑 `index.html`
- 修改交互行为：编辑 `js/main.js`
