<!DOCTYPE html>
<!-- saved from url=(0346)https://wxapi.cppu.edu.cn/cgi-bin/wxlogin/wxqrconnect.php?appid=cppusfrz01&scope=snsapi_login&redirect_uri=&token=UEq1j4bxcH&state=login&login_type=jssdk&embedded=true&cgi_token=375236D7DFB544C1C5AB2564E9C08ED52DAC6AE5E9C19D6A0C18F10207077E21E4F1F266587D2CF83BE68F716DA93AA3BA6BBAF36302A878CE9CCE46CD33445E8CBB0B5579B50AFA2E6375660999&style=white -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>微信登录</title>
				
		<link rel="stylesheet" href="./wxqrconnect.css">
		<link href="https://wxapi.cppu.edu.cn/cgi-bin/image/scan/icon_wechat.ico" rel="Shortcut Icon">
		<script src="./jquery-3.7.1.min.js.下载"></script>
	<style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border-radius: 12px;
  width: 350px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style></head>
	<body>
		<div class="main impowerBox" style="background-color:white">
			<div class="loginPanel normalPanel">				
				<div class="waiting panelContent">
					<div class="wrp_code">
						<div class="qrdescbox qr_disabled" style="display:none;">
							<div><i class="iconfont icon-warning fresh_icon"></i></div>接入账号不合规请求次数超限，限制使用，请联系管理员！&nbsp;&nbsp;<!-- <a class="refresh" onclick="fresh()">刷新</a> -->
						</div>
						<div class="qrdescbox qr_error" style="display:none;">
							<div><i class="iconfont icon-warning fresh_icon"></i></div>请求受限&nbsp;&nbsp;<a class="refresh" onclick="fresh()">刷新</a>
						</div>
						<div class="qrdescbox qr_valid" style="display:none;">
							<div><i class="iconfont icon-warning fresh_icon"></i></div>二维码已过期&nbsp;&nbsp;<a class="refresh" onclick="fresh()">刷新</a>
						</div>
						<div class="qrdescbox cgi_valid" style="display:none;">
							<div><i class="iconfont icon-warning fresh_icon"></i></div>接入账号已过期
						</div>
						<div class="qrdescbox qr_confirm" style="display:none;">
							<div><i class="iconfont icon-warning fresh_icon"></i></div>已扫码，请在微信中点击「确认」授权登录
						</div>
						<div class="qrdescbox qr_cancel" style="display:none;">
							<div><i class="iconfont icon-warning fresh_icon"></i></div>已取消登录授权，扫码请点击「<a class="refresh" onclick="fresh()">刷新</a>」，退出请关闭页面
						</div>
						<div class="qr_mask"> 
							</div>					
								<img class="qrcode lightBorder" src="./wxthirdqrcode.php">	
							</div>
							<div class="info" style="margin-top:-4px;">
								<div class="status status_browser js_status" id="wx_default_tip">
									<p>同时支持微信扫码登录&nbsp;&nbsp;<a class="refresh" onclick="fresh()">刷新</a></p>
									<input type="hidden" name="apiurl" value="https://wxapi.cppu.edu.cn">
									<p style="color:#F59A23;">统一身份认证系统</p>
									<p>此认证基于&nbsp;<span style="color:#219DE1;">中国人民警察大学</span>&nbsp;微信<!-- <span style="color:#219DE1;"></span> --></p>
								</div>
								<div class="status status_succ js_status" style="display:none" id="wx_after_scan">
									<i class="status_icon icon38_msg succ"></i>
									<div class="status_txt">
									<h4>扫描成功</h4>
									<p>请在微信中点击确认即可登录</p>
								</div>
							</div>
							<div class="status status_fail js_status" style="display:none" id="wx_after_cancel">
								<i class="status_icon icon38_msg warn"></i>
								<div class="status_txt">
									<h4>已取消登录授权</h4>
									<p>扫码请点击刷新，退出请关闭页面</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		<script src="./xss209118.js.下载"></script>
		<script>
						var uuid = "UEq071j414bxcH2025";
				var domain = "https://wxapi.cppu.edu.cn";
				var wsdomain = "wss://wxwss.cppu.edu.cn";
				var appdomain = "https://wxapi.cppu.edu.cn";
				var ssoticket = "UEq1j4bxcH";
				var ucode = "cppusfrz01";
				var qr_validtime =  300;
				var validtime = qr_validtime * 1000;
				setInterval( refresh,validtime );
						var ws, wsTid = null;
			var activeClose = false; // 主动关闭连接
			var reconnectTimes = 0;  // 计算重新连接次数
			function startSocket() {
				if ("WebSocket" in window) {
            if (typeof ws != 'undefined'){
							if(null != ws) {
                ws.close();
							}
                null != wsTid && window.clearInterval(wsTid);
            }

            ws = new WebSocket(wsdomain + "/websocket?ucode="+ucode+"&jsqrcode_ticket=" + ssoticket);
            ws.onopen = function() {
                console.log('websocket 已连接上！');
            };

            ws.onmessage = function(e) {
                var result = JSON.parse(e.data);
								// console.log(result);
								if (result.type == 'state') {
									let res = result.data;
									switch(res.flag){
										case 405:
											res.uuid = uuid;
											// window.parent.postMessage(res, appdomain);
											window.parent.postMessage(res, '*');
											console.log('post 数据给外部', res);
											break;
										case 406:
											$(".qr_confirm").show();	
											$(".qr_confirm").siblings(".qrdescbox").hide();
											$(".qr_mask").show();
										break;
										case 407:
											$(".qr_cancel").show();	
											$(".qr_cancel").siblings(".qrdescbox").hide();
											$(".qr_mask").show();
										break;
										case 409:			
											$(".qr_valid").show();	
											$(".qr_error").hide();
											$(".cgi_valid").hide();	
											$(".qr_valid").siblings(".qrdescbox").hide();
											$(".qr_mask").show();
											clearInterval(interval);//停止
											closeWs();
										break;
										case 413:			
											$(".cgi_valid").show();	
											$(".qr_valid").hide();	
											$(".qr_error").hide();
											$(".cgi_valid").siblings(".qrdescbox").hide();
											$(".qr_mask").show();
											clearInterval(interval);//停止
											closeWs();
										break;					
										case 421:
											$(".qr_error").show();	
											$(".qr_valid").hide();
											$(".cgi_valid").hide();	
											$(".qr_error").siblings(".qrdescbox").hide();
											$(".qr_mask").show();
											clearInterval(interval);//停止
											closeWs();
										break;
										case "E3006":
											$(".qr_disabled").show();	
											$(".qr_disabled").siblings(".qrdescbox").hide();
											$(".qr_mask").show();
											clearInterval(interval);//停止
											closeWs();
										break;
										case 422:
											clearInterval(interval);//停止
											closeWs();
										break;
										default:
										closeWs();						
									} 
								}
            };

            ws.onclose = function() {
                console.log('websocket 连接已关闭！');
                null != wsTid && window.clearInterval(wsTid);
								if (!activeClose) {
									reconnectTimes += 1
									if (reconnectTimes == 5) {
										// 通知客户端，长连接重连超过10次了。
										var res = {}
										res.flag = 501;
										res.message = '请检查长连接服务'
										// window.parent.postMessage(res, appdomain);
										window.parent.postMessage(res, '*');
									}
									setTimeout(() => {
										startSocket();
									}, 1000);
								}
            };

            // 发送心跳
            wsTid = window.setInterval( function () {
                if (typeof ws != 'undefined') {
									let heartBeat = {
										type: "ping",
										data: new Date().getTime()
									}
									ws.send(JSON.stringify(heartBeat));
								}
            }, 30000 );
        } else {
            // todo: 不支持 WebSocket 的，可以使用 js 轮询处理，这里不作该功能实现！
            alert('您的浏览器不支持 扫码登录！请使用谷歌浏览器访问');
        }
			}
			function closeWs() {
				activeClose = true;
				if (typeof ws != 'undefined'){
					if(null != ws) {
						ws.close();
					}
						null != wsTid && window.clearInterval(wsTid);
				}
			}
			$(function() {
				startSocket();
			});
			
				function refresh(){

					$(".qr_valid").show();
					$(".qr_disabled").hide();
					$(".qr_error").hide();
					$(".cgi_valid").hide();
					$(".qr_confirm").hide();
					$(".qr_cancel").hide();
					$(".qr_mask").show();
					// 超时关闭长连接

				}

				function fresh(){
					window.location.reload();
					
				}
					</script>
	

</body></html>