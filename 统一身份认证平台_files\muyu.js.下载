function get_sso_token()
{
    // 瀵嗙爜瀛楃闆嗭紝鍙换鎰忔坊鍔犱綘闇€瑕佺殑瀛楃
    var chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    var str = "";
    for(i = 0; i < 10; i++)
    {
        str += chars[random(0,chars.length-1)];
    }
    return str;
    // var reg = /^[a-zA-Z0-9]*$/;
    // if(reg.test(chars)){
    // 	var str = "";
    // 	for(i = 0; i < 10; i++)
    // 	{
    // 	    str += chars[random(0,chars.length-1)];
    // 	}
    // 	return str;
    // }
    // else{
    // 	var str = "闈炴硶瀛楃鏀诲嚮";
    // 	return str;
    // }
}

function get_sso_qrcodeticket()
{
    // 瀵嗙爜瀛楃闆嗭紝鍙换鎰忔坊鍔犱綘闇€瑕佺殑瀛楃
    var chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    var str = "";
    for(i = 0; i < 10; i++)
    {
        str += chars[random(0,chars.length-1)];
    }
    return str;
    // var reg = /^[a-zA-Z0-9]*$/;
    // if(reg.test(chars)){
    // 	var str = "";
    // 	for(i = 0; i < 10; i++)
    // 	{
    // 	    str += chars[random(0,chars.length-1)];
    // 	}
    // 	return str;
    // }
    // else{
    // 	var str = "闈炴硶瀛楃鏀诲嚮";
    // 	return str;
    // }
}

function random(min, max) {
    var reg = /^[0-9]*$/;
    if(reg.test(min) && reg.test(max) ){
        return Math.floor(Math.random() * (max - min)) + min;
    }
    else{
        return "闈炴硶瀛楃鏀诲嚮";
    }

}

!function(a,b){
    function d(a)
    {
        if( typeof( a.token ) == "undefined" ){
            token = a.jsqrcode_ticket;
        }
        else{
            token = a.token;
        }
        var e,c=b.createElement("iframe"),
            d=a.domain+"/wxlogin/wxqrconnect.php?appid="+a.ucode
                +"&scope="+a.scope+"&redirect_uri="+a.redirect_uri
                +"&token="+token+"&state="+a.state+"&login_type=jssdk&embedded=true"
                +"&cgi_token="+a.cgi_token;
        d+=a.style?"&style="+a.style:"",
            d+=a.href?"&href="+a.href:"",
            c.src=d,c.frameBorder="0",
            c.allowTransparency="true",
            c.scrolling="no",
            c.width="300px",
            c.height="330px",
            c.style="margin: -18px;",
            e=b.getElementById(a.id),
            e.innerHTML="",
            e.appendChild(c)
    }
    a.QrLogin=d
}(window,document);

