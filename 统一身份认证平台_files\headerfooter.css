.header{
  /*    position: fixed;
      top:0;
      left: 0;
      right:0;*/
  height: 9.4rem;
  background: white;
  }
  .container{
  /*    position: fixed;
      top:115px;
      bottom: 137px;
      left: 0;
      right: 0;*/
      width: 100%;
       height: calc(100vh - 8.4rem - 9.4rem);

  }
  
  .footer{
   /*   position: fixed;
      z-index: 3;
      bottom:0;
      left: 0;
      right:0;*/
   height: 8.4rem;
   color: #333;
   /* background: url("../images/back_line.gif"); */
  }
  .login{/*          height: 49.6rem; */position: absolute;/* top:9rem !important;  */
    top:calc((100vh - 9.4rem - 8.4rem)/8) ;
    right:7.1rem;z-index: 2;display:flex;background:#fff;border-radius: .75rem;background: rgba(0,0,0,0.5)}
  .login_seen{/*         background: rgb(101,101,101); *//*         filter:Alpha(opacity=90); *//* background: rgba(50,50,50,0.5); *//* background: #fff; *//* width: 330px; *//*         width: 33rem; *//* padding: 20px 50px; */padding: 1rem 2.5rem;/* border-radius: 25px; */border-radius: 25px 0 0 25px;width: 16.8rem;padding: 0rem 3.85rem 0 2.7rem;text-align: center;}
  .banner{
      position: relative;
      width: 100%;
      /* height: 100%; */
  }
  .banner-see{
      width: 100%;
      overflow: hidden;
      position: relative;
      min-height: 20rem;
  }
  .banner-see{
  height: calc(100vh - 9.4rem - 8.4rem ) !important
}
.banner-pic-lists img{
  height:calc(100vh - 9.4rem - 8.4rem ) !important;
}

  .banner-pic-lists{
      /* overflow: auto; */
      position: absolute;
      height:100%;
      top:0;
      left: 0;
      width: 100%;
  }
  .banner-pic-list{
      height: 100%;
      /* float: left; */
      width: 100%;
  }
  .banner-pic-list img{
      display: block;
      width: 100%;
       height:calc(100vh - 9.4rem - 8.4rem ) !important;
      min-height:40rem;
  }
  @media screen and (min-height: 950px){
      .container{
          background: white;
/*             padding-top: 63px; */
      }
/*         .footer{padding-top:90px;
      } */
  }
  @media screen and (min-height: 750px)and (max-height: 880px){
      .container{
          background: white;
          /* padding-top: 50px; */
      }
  }
  @media screen and (max-width: 3000px) {
      html{
        /* font-size: 28px !important; */
      }
    }


    @media screen and (max-width: 2700px) {
      html{
        /* font-size: 26px !important; */
      }
    }
  @media screen and (max-width: 2500px) {
      html{
        /* font-size: 24px !important; */
      }
    }
    @media screen and (max-width: 2400px) {
      html{
        /* font-size: 23px !important; */
      }
    }

    @media screen and (max-width: 2300px) {
      html{
        /* font-size: 22px !important; */
      }
    }
    @media screen and (max-width: 2200px) {
      html{
        /* font-size: 21px !important; */
      }
    }

  @media screen and (max-width: 2000px) {
      html{
        /* font-size: 20px !important; */
      }
    }
  @media screen and (max-width: 1700px) {
      html{
        /* font-size: 17.4px !important; */
      }
    }
    @media screen and (max-width: 1600px) {
      html{
        /* font-size: 17px !important; */
      }
    }
    @media screen and (max-width: 1440px) {
      html{
        /* font-size: 14px !important; */
      }
      .login{
        max-height: 25.3rem;
        /* top: calc((100vh - 9.4rem - 10.4rem)/16) !important; */
      }
    }
    @media screen and (max-height: 700px) {
      .login{
        max-height: 22.8rem;
      }
    }

    @media screen and (max-width: 1300px) {
      html{
        /* font-size: 12.6px !important; */
      }
      .login{
        max-height: 25.3rem;
        /* top: calc((100vh - 9.4rem - 10.4rem)/16) !important; */
      }
    }
  
    @media screen and (min-width: 3000px) {
      html{
        /* font-size: 32px !important; */
      }
    }