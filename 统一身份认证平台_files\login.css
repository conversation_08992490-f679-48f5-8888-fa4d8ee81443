html,body,div,p,span,ul,li,img,i,h1,h2,h3,h4,h5,h6,a,input,button{
    padding: 0;
    margin: 0;
}
html{font-size:14px}
@media screen and (min-width:321px) and (max-width:375px){html{font-size:11px}}
@media screen and (min-width:376px) and (max-width:414px){html{font-size:12px}}
@media screen and (min-width:415px) and (max-width:639px){html{font-size:15px}}

img{
    border: 0;
}
.codeImg{
    height:2.1rem
}
body {
    font-family: "瀵邦喛钂嬮梿鍛寸拨";
    height: 100vh;
    /* background: url("../images/back_line.gif"); */
}
input,button{
    /*-webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;*/
    outline: 0;
    border: 0;
    font-size: 16px;
}
input:-webkit-autofill {
    background-color: white;
}
li{
    list-style: none;
}
a,a:hover,a:visited,a:active{
    text-decoration: none;
    /* color: #333; */
    border-color: #1D319E;
}
.center{
    height: 100%;
    padding: 0 9rem 0 10rem;
}
.logo_pic{
    float: left;
    padding: 3.3rem 0 2.1rem;
}
.logo{
    display: block;
    /* width: 100%; */
    /* width: 38.1rem; */
    height: 4rem;
}
.header_right{
    float: right;
}
.header_time{
   margin-top: 4.5rem;
   text-align: center;
   line-height: 2.7rem;
   /* height: 6.4rem; */
   background-color:  #1d31a0;
   color:white;
   font-size: max(12px,.8rem);
   /* width: 14.5rem; */
   padding: 0 1.4rem;
   /* border-bottom-left-radius: 1.5rem;
    border-bottom-right-radius: 1.5rem; */
   border-radius: .75rem;
}
.header_gonggao{
    display:none;
    text-align: right;
    padding-top: .85rem;
}
.header_gonggao a{
    padding-left:1.4rem ;
    background:url("../images/ggicon.png") no-repeat left center;
    font-size: max(12px,.8rem);
    color: #0571bc;
}
/*娴犮儰绗傛径鎾劥*/
.login_header{
    /* color: white; */
    color:'#393939';
    font-weight: bold;
    /* border-bottom: 1px solid #fff; */
    /* font-size: 1.8rem; */
    /* line-height: 4rem; */
    color: #fff;
    font-size: max(12px,1rem);
    padding: 2.2rem 0 1.45rem;
    letter-spacing: 2px;
}
.login_header span{
    cursor: pointer;
    display: inline-block;
    border-bottom: 0.15rem solid transparent;
    margin-bottom: -0.2rem;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.login_header span:first-child{
    margin-right: 1rem;
}

.login_header .middle{
    margin-right: 1rem;
}
.login_header span.active{
    border-color: #fff;
}
.login_form{
    /* padding-top: 1.5rem; */
}
.codeCon{
    display:flex
}
.Verification{
    margin-right:.5rem
}

.fill_form{
    margin-bottom: .75rem;
    padding: 0.25rem .75rem;
    /* background-color: #fff; */
    background-color: #f6f6f6;
    border-radius: 0.2rem;
    overflow: auto;
    display:flex
}
.fill_form input{
    font-size:max(12px,.75rem);
    background-color: #f6f6f6;
    display: block;
    float: left;
    /* width: calc(100% - 7.5rem); */
    line-height: 1.6rem;
    height: 1.6rem;
    width: 100%;
}
.fill_form_other{
   float: left;
   line-height: 1.6rem;
   height: 1.6rem;
   overflow: hidden;
   width: 1.5rem;
}
.fill_form_other img{
    display: block;
    width: 100%;
}
.fill_form_other1{
    background: url("../images/l-username.png") no-repeat left;
}
.fill_form_other2{
    background: url("../images/l-password.png") no-repeat left;
}
.fill_form_other3{
    background: url("../images/code.png") no-repeat left;
    background-size: 16px;
}

.fill_form.code{
    margin-bottom: 5px;
}
.auto-login{
    color: white;
    margin-bottom: 5px;
    line-height: 20px;
}
.auto-login input{
    -webkit-appearance: checkbox;
    -moz-appearance: checkbox;
    appearance: checkbox;
    vertical-align: middle;
}
.login_erweima{
    padding-bottom: 15px;
    overflow: hidden;
}
.login_erweima img{
       display: block;
    height: 10rem;
    width:10rem;
    margin: 0 auto;
    margin: 1.35rem 0 1.75rem;
}
.login_btn{
   display: block;
    height: 2.1rem;
    text-align: center;
    color: white;
    width: 100%;
    font-size: max(12px,.9rem);
    background-color:  #1d31a0;
    border-radius: .2rem;
    cursor: pointer;
}
.login_other{
    text-align: center;
    line-height: 2.1rem;
    position: relative;
    height:2.1rem;
    font-size: max(12px,.5rem);
    /* color: #fff; */
}
.login_other a{
    color: #fff;
}
.login_other a:hover{
    text-decoration: underline;
}
.login_other a:first-child{
    position: absolute;
    left: 0;
    top:0;
}
.login_other a.last{
    position: absolute;
    right: 0;
    top:0;
}
.footer_tishi{
    text-align: center;
     padding-top: 3.7rem;
    letter-spacing: .1rem;
    font-size: max(12px,.5rem);
}
.footer_tishi span:nth-child(3){
    margin-left: 1rem;
}
.copyright{
    text-align: center;
    margin-top: 1rem;
}
.copyright img{
    vertical-align: middle;
    margin-right: 0.1rem;
}

.logo-mobile{
    display:none
}
/*闁倿鍘ゅ鈧慨锟�*/
@media screen and (min-height: 1367px){
    .login_seen {
        /* margin-top: 15rem; */
    }
}

@media screen and (max-width: 939px){
   .center{
       padding: 0 1rem;
   }
}
@media screen and (max-width: 899px){
    .footer_tishi span{
        display: block;
        width: 26rem;
        margin: 0 auto;
        text-align: left;
    }
    .footer_tishi span:nth-child(2),.footer_tishi span:nth-child(3) {
        width: 24.5rem;
        margin: 0 auto;
        padding-left: 1.5rem;
    }
    .copyright{
        margin-top: .5rem;
    }
}
@media screen and (max-width: 866px){
    .logo_pic{
        width: calc(100% - 14.5rem);
    }
}
@media screen and (max-width: 520px){
    .form-qr-code.login_form.login_form2{
        display:none!important;
    }
    .login_seen{
        width:unset;
        border-radius:1.25rem
    }
   .logo{
    display:none
   }
}

.displaynone{
    display: none;
}

.form-error{
    text-align: left;
    color: #ff2424;
    font-size: max(12px,.75rem);
    margin-bottom: 0.25rem;
}


.form-qr-code{
    /* display: none; */
    display:flex !important;
    /* background:#fff; */
    border-radius:  0 1.25rem 1.25rem 0;
    text-align:center;
    padding-right:2.7rem
}
.login_erweima .qrcode-title{
    color:#1d31a0;
    text-align: center;
    font-weight: 600;
    color: #fff;
    font-size:max(12px, 1rem);
    padding: 2.2rem 0 1.45rem;
    letter-spacing: 2px;
}
.qr-code img{
    width: 80%;
}
/* .qr-code p */
.login_erweima p{
    /* color:#fff; */
    text-align:center;
    color:#393939;
    font-size: max(12px,.7rem);
    margin-top: .5rem;
	text-align:center;
}

.gray {  
    color: #e9e9e9;  
    border: solid 1px #555;  
    background: #6e6e6e;  
}  

.button {
    width: 29%;
    height: 1.7rem;
    line-height: 1.7rem;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    text-align: center;
    /*font: 14px/100% Arial, Helvetica, sans-serif;  */
    border-radius: .25em;
    -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.2);
    -moz-box-shadow: 0 1px 2px rgba(0,0,0,.2);
    box-shadow: 0 1px 2px rgba(0,0,0,.2);
}

.way{
    margin-bottom: 10px;
    color: white;
    font-size:max(12px, .5rem);
}

#qqimg{
    width: 24px;
    margin-right: 10px;
}
#weiboimg{
    width: 24px;
}

#rememberMe{
    cursor: pointer;
    margin-left: 0px;
    width: 13px;
	height:13px;
    border-radius:0;
}
.rememberdiv{
    text-align: left;
    /* color:#ffffff; */
    color: #fff;
    margin: .6rem 0 1.25rem;
}

.rememberdiv span{
    cursor: pointer;
    font-size:max(12px, .6rem);
}


.jumptip{
    color: #ffffff;
    font-size: 18px;
    width: 100%;
    margin-bottom: 30px;
}


#msg{
    color: #000000;
    font-size: 15px;
    margin-top: 30px;
    margin-bottom: 30px;
}

#msg p {
    color: #000000;;
    margin-bottom: 20px;
}

#msg h2 {
    color: #000000;
    margin-bottom: 20px;
}


#msg a {
    color: #ff2424;
    border: #0b70d8;
    text-decoration: underline;
}


#msg p a {
    color: #ff2424;
    border: #0b70d8;
    text-decoration: underline;
}

#msg p a span{
    color: #ff2424;
    border: #0b70d8;
    text-decoration: underline;
}
 .mobilelogo{display:none}
 #send_button{
    font-size: max(12px,.5rem);
 }